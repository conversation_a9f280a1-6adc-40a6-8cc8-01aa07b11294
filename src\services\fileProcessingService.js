const fs = require('fs-extra');
const path = require('path');
const unzipper = require('unzipper');
const { spawn } = require('child_process');
const {
  getExtractTempDir,
  getFinalDir,
  removeDirectory,
  ensureDirectoryExists,
  generateTimestamp
} = require('../utils/fileUtils');
const logger = require('../utils/logger');
const config = require('../config');

class FileProcessingService {
  /**
   * Xử lý file: giải nén, copy, dọn dẹp
   */
  async processFile(jobData, updateProgress) {
    const { jobId, filePath, filename, userId, uploadId } = jobData;
    
    try {
      logger.logJob(jobId, 'Starting file processing', { filename, filePath });
      
      // Bước 1: Tạ<PERSON> thư mục tạm để giải nén
      await updateProgress(10, 'Creating temporary directories');
      const extractDir = getExtractTempDir(jobId);
      await ensureDirectoryExists(extractDir);
      
      // Bước 2: Gi<PERSON><PERSON> nén file
      await updateProgress(20, 'Extracting file');
      const extractResult = await this.extractFile(filePath, extractDir, filename);
      
      // Bước 3: Tạo thư mục đích
      await updateProgress(60, 'Creating destination directory');
      const timestamp = generateTimestamp();
      const finalDir = getFinalDir(userId, timestamp);
      await ensureDirectoryExists(finalDir);
      
      // Bước 4: Copy dữ liệu đến thư mục đích
      await updateProgress(70, 'Copying files to destination');
      await this.copyToDestination(extractDir, finalDir);
      
      // Bước 5: Dọn dẹp file tạm
      await updateProgress(90, 'Cleaning up temporary files');
      await this.cleanup(uploadId, extractDir);
      
      await updateProgress(100, 'Processing completed');
      
      const result = {
        success: true,
        finalPath: finalDir,
        extractedFiles: extractResult.extractedFiles,
        totalSize: extractResult.totalSize,
        processedAt: new Date().toISOString()
      };
      
      logger.logJob(jobId, 'File processing completed successfully', result);
      return result;
      
    } catch (error) {
      logger.logError('FileProcessingService.processFile', error, { jobId, filename });
      
      // Dọn dẹp khi có lỗi
      try {
        const extractDir = getExtractTempDir(jobId);
        await this.cleanup(uploadId, extractDir);
      } catch (cleanupError) {
        logger.logError('FileProcessingService.cleanup', cleanupError, { jobId });
      }
      
      throw error;
    }
  }

  /**
   * Giải nén file
   */
  async extractFile(filePath, extractDir, filename) {
    const ext = path.extname(filename).toLowerCase();
    
    logger.info(`Extracting ${filename} to ${extractDir}`);
    
    try {
      let extractedFiles = [];
      let totalSize = 0;
      
      if (ext === '.zip') {
        const result = await this.extractZip(filePath, extractDir);
        extractedFiles = result.files;
        totalSize = result.totalSize;
      } else if (ext === '.rar') {
        const result = await this.extractRar(filePath, extractDir);
        extractedFiles = result.files;
        totalSize = result.totalSize;
      } else if (ext === '.7z') {
        const result = await this.extract7z(filePath, extractDir);
        extractedFiles = result.files;
        totalSize = result.totalSize;
      } else {
        // Nếu không phải file nén, copy trực tiếp
        const destPath = path.join(extractDir, filename);
        await fs.copy(filePath, destPath);
        const stats = await fs.stat(destPath);
        extractedFiles = [filename];
        totalSize = stats.size;
      }
      
      return { extractedFiles, totalSize };
    } catch (error) {
      throw new Error(`Failed to extract ${filename}: ${error.message}`);
    }
  }

  /**
   * Giải nén file ZIP
   */
  async extractZip(filePath, extractDir) {
    return new Promise((resolve, reject) => {
      const files = [];
      let totalSize = 0;
      
      fs.createReadStream(filePath)
        .pipe(unzipper.Parse())
        .on('entry', (entry) => {
          const fileName = entry.path;
          const type = entry.type;
          const size = entry.size;
          
          if (type === 'File') {
            files.push(fileName);
            totalSize += size;
            entry.pipe(fs.createWriteStream(path.join(extractDir, fileName)));
          } else {
            entry.autodrain();
          }
        })
        .on('finish', () => {
          resolve({ files, totalSize });
        })
        .on('error', reject);
    });
  }

  /**
   * Giải nén file RAR
   */
  async extractRar(filePath, extractDir) {
    return new Promise((resolve, reject) => {
      const unrar = spawn('unrar', ['x', filePath, extractDir]);
      
      unrar.on('close', async (code) => {
        if (code === 0) {
          try {
            const files = await this.getExtractedFiles(extractDir);
            const totalSize = await this.calculateTotalSize(extractDir);
            resolve({ files, totalSize });
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error(`unrar process exited with code ${code}`));
        }
      });
      
      unrar.on('error', reject);
    });
  }

  /**
   * Giải nén file 7Z
   */
  async extract7z(filePath, extractDir) {
    return new Promise((resolve, reject) => {
      const seven = spawn('7z', ['x', filePath, `-o${extractDir}`]);
      
      seven.on('close', async (code) => {
        if (code === 0) {
          try {
            const files = await this.getExtractedFiles(extractDir);
            const totalSize = await this.calculateTotalSize(extractDir);
            resolve({ files, totalSize });
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error(`7z process exited with code ${code}`));
        }
      });
      
      seven.on('error', reject);
    });
  }

  /**
   * Lấy danh sách file đã giải nén
   */
  async getExtractedFiles(dir) {
    const files = [];
    
    const scan = async (currentDir, relativePath = '') => {
      const items = await fs.readdir(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const relPath = path.join(relativePath, item);
        const stats = await fs.stat(fullPath);
        
        if (stats.isDirectory()) {
          await scan(fullPath, relPath);
        } else {
          files.push(relPath);
        }
      }
    };
    
    await scan(dir);
    return files;
  }

  /**
   * Tính tổng kích thước file
   */
  async calculateTotalSize(dir) {
    let totalSize = 0;
    
    const scan = async (currentDir) => {
      const items = await fs.readdir(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stats = await fs.stat(fullPath);
        
        if (stats.isDirectory()) {
          totalSize += await scan(fullPath);
        } else {
          totalSize += stats.size;
        }
      }
    };
    
    await scan(dir);
    return totalSize;
  }

  /**
   * Copy dữ liệu đến thư mục đích
   */
  async copyToDestination(sourceDir, destDir) {
    try {
      await fs.copy(sourceDir, destDir);
      logger.info(`Files copied from ${sourceDir} to ${destDir}`);
    } catch (error) {
      throw new Error(`Failed to copy files: ${error.message}`);
    }
  }

  /**
   * Dọn dẹp file và thư mục tạm
   */
  async cleanup(uploadId, extractDir) {
    try {
      // Xóa thư mục upload tạm
      if (uploadId) {
        const uploadDir = path.join(config.paths.tempUploadDir, uploadId);
        await removeDirectory(uploadDir);
      }
      
      // Xóa thư mục giải nén tạm
      if (extractDir) {
        await removeDirectory(extractDir);
      }
      
      logger.info('Cleanup completed successfully');
    } catch (error) {
      logger.logError('FileProcessingService.cleanup', error);
      // Không throw error ở đây vì cleanup không quan trọng bằng kết quả chính
    }
  }
}

module.exports = new FileProcessingService();
