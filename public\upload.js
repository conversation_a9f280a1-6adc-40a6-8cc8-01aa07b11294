class FileUploader {
    constructor() {
        this.chunkSize = 10 * 1024 * 1024; // 10MB
        this.uploadId = null;
        this.jobId = null;
        this.currentFile = null;
        
        this.initElements();
        this.bindEvents();
    }
    
    initElements() {
        this.fileInput = document.getElementById('fileInput');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.progressSection = document.getElementById('progressSection');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        this.statusSection = document.getElementById('statusSection');
        this.statusCards = document.getElementById('statusCards');
    }
    
    bindEvents() {
        this.fileInput.addEventListener('change', (e) => {
            this.currentFile = e.target.files[0];
            this.updateUploadButton();
        });
        
        this.uploadBtn.addEventListener('click', () => {
            if (this.currentFile) {
                this.startUpload();
            }
        });
    }
    
    updateUploadButton() {
        if (this.currentFile) {
            this.uploadBtn.textContent = `🚀 Upload ${this.currentFile.name} (${this.formatFileSize(this.currentFile.size)})`;
            this.uploadBtn.disabled = false;
        } else {
            this.uploadBtn.textContent = '🚀 Bắt đầu Upload';
            this.uploadBtn.disabled = true;
        }
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    async startUpload() {
        try {
            this.showProgress();
            this.uploadBtn.disabled = true;
            this.uploadBtn.textContent = '⏳ Đang upload...';
            
            // Bước 1: Khởi tạo upload
            await this.initUpload();
            
            // Bước 2: Upload chunks
            await this.uploadChunks();
            
        } catch (error) {
            this.showError('Lỗi upload: ' + error.message);
            this.resetUpload();
        }
    }
    
    async initUpload() {
        const totalChunks = Math.ceil(this.currentFile.size / this.chunkSize);
        
        const response = await fetch('/api/upload/init-upload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                filename: this.currentFile.name,
                totalChunks: totalChunks,
                totalSize: this.currentFile.size,
                userId: 'demo-user'
            })
        });
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.error);
        }
        
        this.uploadId = result.data.uploadId;
        console.log('Upload initialized:', this.uploadId);
    }
    
    async uploadChunks() {
        const totalChunks = Math.ceil(this.currentFile.size / this.chunkSize);
        let uploadedChunks = 0;
        
        for (let i = 0; i < totalChunks; i++) {
            const start = i * this.chunkSize;
            const end = Math.min(start + this.chunkSize, this.currentFile.size);
            const chunk = this.currentFile.slice(start, end);
            
            await this.uploadChunk(i, chunk);
            
            uploadedChunks++;
            const progress = Math.round((uploadedChunks / totalChunks) * 100);
            this.updateProgress(progress, `Đã upload ${uploadedChunks}/${totalChunks} chunks`);
        }
    }
    
    async uploadChunk(chunkIndex, chunkData) {
        const formData = new FormData();
        formData.append('uploadId', this.uploadId);
        formData.append('chunkIndex', chunkIndex);
        formData.append('chunk', chunkData);
        
        const response = await fetch('/api/upload/upload-chunk', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.error);
        }
        
        // Nếu upload hoàn tất và có jobId
        if (result.data.isComplete && result.data.jobId) {
            this.jobId = result.data.jobId;
            this.showSuccess('Upload hoàn tất! Đang xử lý file...');
            this.startJobMonitoring();
        }
        
        return result;
    }
    
    startJobMonitoring() {
        if (!this.jobId) return;
        
        this.showStatus();
        this.monitorJob();
        
        // Poll job status every 2 seconds
        this.jobInterval = setInterval(() => {
            this.monitorJob();
        }, 2000);
    }
    
    async monitorJob() {
        try {
            const response = await fetch(`/api/jobs/${this.jobId}/status`);
            const result = await response.json();

            if (result.success) {
                this.updateJobStatus(result.data);

                // Stop monitoring if job is completed or failed
                if (result.data.status === 'completed' || result.data.status === 'failed') {
                    clearInterval(this.jobInterval);
                    this.resetUpload();
                }
            }
        } catch (error) {
            console.error('Error monitoring job:', error);
        }
    }

    updateJobStatus(jobData) {
        const statusClass = `status-${jobData.status}`;
        const statusText = this.getStatusText(jobData.status);

        let resultHtml = '';
        if (jobData.result && jobData.status === 'completed') {
            resultHtml = `
                <div style="margin-top: 10px;">
                    <strong>📁 Thư mục đích:</strong> ${jobData.result.finalPath}<br>
                    <strong>📊 Số file giải nén:</strong> ${jobData.result.extractedFiles?.length || 0}<br>
                    <strong>💾 Tổng dung lượng:</strong> ${this.formatFileSize(jobData.result.totalSize || 0)}
                </div>
            `;
        }

        let errorHtml = '';
        if (jobData.error && jobData.status === 'failed') {
            errorHtml = `
                <div class="error" style="margin-top: 10px;">
                    <strong>❌ Lỗi:</strong> ${jobData.error}
                </div>
            `;
        }

        let logsHtml = '';
        if (jobData.logs && jobData.logs.length > 0) {
            logsHtml = `
                <div class="logs">
                    ${jobData.logs.map(log => `<div>${log}</div>`).join('')}
                </div>
            `;
        }

        this.statusCards.innerHTML = `
            <div class="status-card">
                <div class="status-title">
                    🔄 Job Processing
                    <span class="job-status ${statusClass}">${statusText}</span>
                </div>
                <div class="status-info">
                    <strong>Job ID:</strong> ${jobData.jobId}<br>
                    <strong>Tiến độ:</strong> ${jobData.progress || 0}%<br>
                    <strong>Tạo lúc:</strong> ${new Date(jobData.createdAt).toLocaleString()}<br>
                    ${jobData.processedAt ? `<strong>Bắt đầu xử lý:</strong> ${new Date(jobData.processedAt).toLocaleString()}<br>` : ''}
                    ${jobData.finishedAt ? `<strong>Hoàn thành:</strong> ${new Date(jobData.finishedAt).toLocaleString()}<br>` : ''}
                </div>
                ${resultHtml}
                ${errorHtml}
                ${logsHtml}
            </div>
        `;
    }

    getStatusText(status) {
        const statusMap = {
            'pending': 'Đang chờ',
            'running': 'Đang xử lý',
            'completed': 'Hoàn thành',
            'failed': 'Thất bại'
        };
        return statusMap[status] || status;
    }

    showProgress() {
        this.progressSection.style.display = 'block';
        this.updateProgress(0, 'Bắt đầu upload...');
    }

    updateProgress(percent, message) {
        this.progressFill.style.width = percent + '%';
        this.progressText.textContent = `${percent}% - ${message}`;
    }

    showStatus() {
        this.statusSection.style.display = 'block';
    }

    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error';
        errorDiv.textContent = message;
        this.statusCards.appendChild(errorDiv);
        this.showStatus();
    }

    showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success';
        successDiv.textContent = message;
        this.statusCards.appendChild(successDiv);
        this.showStatus();
    }

    resetUpload() {
        this.uploadBtn.disabled = false;
        this.uploadBtn.textContent = '🚀 Bắt đầu Upload';
        this.uploadId = null;
        this.jobId = null;

        if (this.jobInterval) {
            clearInterval(this.jobInterval);
            this.jobInterval = null;
        }
    }
}

// Khởi tạo uploader khi DOM ready
document.addEventListener('DOMContentLoaded', () => {
    new FileUploader();
});
