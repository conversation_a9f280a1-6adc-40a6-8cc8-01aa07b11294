const fs = require('fs-extra');
const path = require('path');
const {
  generateUploadId,
  ensureDirectoryExists,
  getUploadTempDir,
  getChunkPath,
  getMetaPath,
  getCompleteFilePath,
  isAllowedExtension,
  getFileSize
} = require('../utils/fileUtils');
const logger = require('../utils/logger');
const config = require('../config');

class UploadService {
  /**
   * Khởi tạo phiên upload mới
   */
  async initUpload(filename, totalChunks, totalSize, userId = 'anonymous') {
    try {
      // Kiểm tra extension
      if (!isAllowedExtension(filename)) {
        throw new Error(`File extension not allowed. Allowed: ${config.upload.allowedExtensions.join(', ')}`);
      }

      // Kiểm tra kích thước file
      if (totalSize > config.upload.maxFileSize) {
        throw new Error(`File too large. Max size: ${config.upload.maxFileSize} bytes`);
      }

      const uploadId = generateUploadId();
      const uploadDir = getUploadTempDir(uploadId);

      // Tạo thư mục upload
      await ensureDirectoryExists(uploadDir);

      // Tạo file meta
      const meta = {
        uploadId,
        filename,
        totalChunks: parseInt(totalChunks),
        totalSize: parseInt(totalSize),
        userId,
        chunksReceived: 0,
        chunksStatus: {},
        createdAt: new Date().toISOString(),
        status: 'initialized'
      };

      const metaPath = getMetaPath(uploadId);
      await fs.writeJson(metaPath, meta);

      logger.logUpload(uploadId, 'Upload initialized', {
        filename,
        totalChunks,
        totalSize,
        userId
      });

      return {
        uploadId,
        chunkSize: config.upload.chunkSize,
        maxFileSize: config.upload.maxFileSize
      };
    } catch (error) {
      logger.logError('UploadService.initUpload', error);
      throw error;
    }
  }

  /**
   * Xử lý upload chunk
   */
  async uploadChunk(uploadId, chunkIndex, chunkFile) {
    try {
      const metaPath = getMetaPath(uploadId);
      
      // Kiểm tra meta file có tồn tại không
      if (!await fs.pathExists(metaPath)) {
        throw new Error('Upload session not found');
      }

      // Đọc meta
      const meta = await fs.readJson(metaPath);
      
      // Kiểm tra chunk index hợp lệ
      if (chunkIndex < 0 || chunkIndex >= meta.totalChunks) {
        throw new Error('Invalid chunk index');
      }

      // Kiểm tra chunk đã được upload chưa
      if (meta.chunksStatus[chunkIndex]) {
        logger.logUpload(uploadId, `Chunk ${chunkIndex} already uploaded, skipping`);
        return {
          success: true,
          message: 'Chunk already uploaded',
          chunksReceived: meta.chunksReceived,
          totalChunks: meta.totalChunks,
          isComplete: meta.chunksReceived === meta.totalChunks
        };
      }

      // Lưu chunk
      const chunkPath = getChunkPath(uploadId, chunkIndex);
      await fs.move(chunkFile.path, chunkPath);

      // Cập nhật meta
      meta.chunksStatus[chunkIndex] = {
        received: true,
        size: chunkFile.size,
        receivedAt: new Date().toISOString()
      };
      meta.chunksReceived += 1;
      meta.lastChunkAt = new Date().toISOString();

      // Kiểm tra đã nhận đủ chunk chưa
      const isComplete = meta.chunksReceived === meta.totalChunks;
      if (isComplete) {
        meta.status = 'chunks_complete';
        meta.completedAt = new Date().toISOString();
      }

      await fs.writeJson(metaPath, meta);

      logger.logUpload(uploadId, `Chunk ${chunkIndex} uploaded`, {
        chunkSize: chunkFile.size,
        chunksReceived: meta.chunksReceived,
        totalChunks: meta.totalChunks,
        isComplete
      });

      return {
        success: true,
        chunksReceived: meta.chunksReceived,
        totalChunks: meta.totalChunks,
        isComplete,
        uploadId
      };
    } catch (error) {
      logger.logError('UploadService.uploadChunk', error, { uploadId, chunkIndex });
      throw error;
    }
  }

  /**
   * Ghép các chunk thành file hoàn chỉnh
   */
  async assembleFile(uploadId) {
    try {
      const metaPath = getMetaPath(uploadId);
      const meta = await fs.readJson(metaPath);

      if (meta.status !== 'chunks_complete') {
        throw new Error('Not all chunks received');
      }

      const completeFilePath = getCompleteFilePath(uploadId, meta.filename);
      const writeStream = fs.createWriteStream(completeFilePath);

      logger.logUpload(uploadId, 'Starting file assembly');

      // Ghép các chunk theo thứ tự
      for (let i = 0; i < meta.totalChunks; i++) {
        const chunkPath = getChunkPath(uploadId, i);
        
        if (!await fs.pathExists(chunkPath)) {
          throw new Error(`Chunk ${i} not found`);
        }

        const chunkData = await fs.readFile(chunkPath);
        writeStream.write(chunkData);
      }

      writeStream.end();

      // Đợi write stream hoàn thành
      await new Promise((resolve, reject) => {
        writeStream.on('finish', resolve);
        writeStream.on('error', reject);
      });

      // Kiểm tra kích thước file
      const assembledSize = await getFileSize(completeFilePath);
      
      logger.logUpload(uploadId, 'File assembly completed', {
        filename: meta.filename,
        expectedSize: meta.totalSize,
        actualSize: assembledSize
      });

      // Cập nhật meta
      meta.status = 'assembled';
      meta.assembledAt = new Date().toISOString();
      meta.assembledSize = assembledSize;
      meta.filePath = completeFilePath;
      await fs.writeJson(metaPath, meta);

      return {
        success: true,
        filePath: completeFilePath,
        filename: meta.filename,
        size: assembledSize,
        meta
      };
    } catch (error) {
      logger.logError('UploadService.assembleFile', error, { uploadId });
      throw error;
    }
  }

  /**
   * Lấy thông tin upload
   */
  async getUploadInfo(uploadId) {
    try {
      const metaPath = getMetaPath(uploadId);
      
      if (!await fs.pathExists(metaPath)) {
        throw new Error('Upload session not found');
      }

      const meta = await fs.readJson(metaPath);
      return meta;
    } catch (error) {
      logger.logError('UploadService.getUploadInfo', error, { uploadId });
      throw error;
    }
  }
}

module.exports = new UploadService();
