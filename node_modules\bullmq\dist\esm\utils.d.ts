/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import { Cluster, Redis } from 'ioredis';
import { AbortController } from 'node-abort-controller';
import { ChildMessage, RedisClient } from './interfaces';
import { EventEmitter } from 'events';
export declare const errorObject: {
    [index: string]: any;
};
export declare function tryCatch(fn: (...args: any) => any, ctx: any, args: any[]): any;
/**
 * Checks the size of string for ascii/non-ascii characters
 * @see https://stackoverflow.com/a/23318053/1347170
 * @param str -
 */
export declare function lengthInUtf8Bytes(str: string): number;
export declare function isEmpty(obj: object): boolean;
export declare function array2obj(arr: string[]): Record<string, string>;
export declare function delay(ms: number, abortController?: AbortController): Promise<void>;
export declare function isRedisInstance(obj: any): obj is Redis | Cluster;
export declare function isRedisCluster(obj: unknown): obj is Cluster;
export declare function increaseMaxListeners(emitter: EventEmitter, count: number): void;
export declare function decreaseMaxListeners(emitter: EventEmitter, count: number): void;
export declare function removeAllQueueData(client: RedisClient, queueName: string, prefix?: string): Promise<void | boolean>;
export declare function getParentKey(opts: {
    id: string;
    queue: string;
}): string | undefined;
export declare const clientCommandMessageReg: RegExp;
export declare const DELAY_TIME_5 = 5000;
export declare const DELAY_TIME_1 = 100;
export declare function isNotConnectionError(error: Error): boolean;
interface procSendLike {
    send?(message: any, callback?: (error: Error | null) => void): boolean;
    postMessage?(message: any): void;
}
export declare const asyncSend: <T extends procSendLike>(proc: T, msg: any) => Promise<void>;
export declare const childSend: (proc: NodeJS.Process, msg: ChildMessage) => Promise<void>;
export declare const isRedisVersionLowerThan: (currentVersion: string, minimumVersion: string) => boolean;
export declare const parseObjectValues: (obj: {
    [key: string]: string;
}) => Record<string, any>;
export declare const errorToJSON: (value: any) => Record<string, any>;
export declare const WORKER_SUFFIX = "";
export declare const QUEUE_EVENT_SUFFIX = ":qe";
export {};
