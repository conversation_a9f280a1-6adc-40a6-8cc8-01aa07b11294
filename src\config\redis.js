const Redis = require('redis');
const { Queue, Worker } = require('bullmq');
const config = require('./index');

// Tạo Redis client
const createRedisClient = () => {
  const client = Redis.createClient({
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
    maxRetriesPerRequest: config.redis.maxRetriesPerRequest,
    retryDelayOnFailover: config.redis.retryDelayOnFailover,
    enableReadyCheck: config.redis.enableReadyCheck
  });

  client.on('error', (err) => {
    console.error('Redis Client Error:', err);
  });

  client.on('connect', () => {
    console.log('Redis Client Connected');
  });

  client.on('ready', () => {
    console.log('Redis Client Ready');
  });

  client.on('end', () => {
    console.log('Redis Client Disconnected');
  });

  return client;
};

// Tạo BullMQ Queue
const createQueue = () => {
  const queue = new Queue(config.queue.name, {
    connection: {
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password
    },
    defaultJobOptions: {
      removeOnComplete: config.queue.removeOnComplete,
      removeOnFail: config.queue.removeOnFail,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000
      }
    }
  });

  queue.on('error', (err) => {
    console.error('Queue Error:', err);
  });

  return queue;
};

// Tạo BullMQ Worker
const createWorker = (processorFunction) => {
  const worker = new Worker(config.queue.name, processorFunction, {
    connection: {
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password
    },
    concurrency: config.queue.maxConcurrentJobs
  });

  worker.on('completed', (job) => {
    console.log(`Job ${job.id} completed successfully`);
  });

  worker.on('failed', (job, err) => {
    console.error(`Job ${job.id} failed:`, err);
  });

  worker.on('error', (err) => {
    console.error('Worker Error:', err);
  });

  return worker;
};

module.exports = {
  createRedisClient,
  createQueue,
  createWorker
};
