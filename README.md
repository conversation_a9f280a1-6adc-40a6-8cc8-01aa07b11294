# GeoPortal File Upload System

Hệ thống upload file lớn với xử lý tác vụ ngầm cho GeoPortal. Hỗ trợ upload file lên đến hàng chục GB thông qua chunk upload và xử lý background jobs.

## Tính năng chính

- ✅ Upload file lớn qua chunk upload
- ✅ Xử lý tác vụ ngầm với BullMQ
- ✅ Tự động giải nén file (ZIP, RAR)
- ✅ Copy dữ liệu đến thư mục đích
- ✅ Dọn dẹp file tạm tự động
- ✅ Theo dõi tiến độ job realtime
- ✅ Xử lý lỗi và logging chi tiết

## Yêu cầu hệ thống

- **Node.js**: >= 16.0.0
- **Redis**: >= 6.0.0 (cho job queue)
- **RAM**: Tối thiểu 2GB (khuyến nghị 4GB+)
- **Ổ cứng**: Đ<PERSON> dung lượng cho file tạm (ít nhất 2x kích thước file lớn nhất)

### Dependencies tùy chọn (cho giải nén)
- **unrar**: Đ<PERSON> giải nén file RAR
- **7z**: Để giải nén file 7Z

## Cài đặt

### 1. Clone repository
```bash
git clone <repository-url>
cd geoportal-file-upload-system
```

### 2. Cài đặt dependencies
```bash
npm install
```

### 3. Cấu hình Redis
Cài đặt và chạy Redis server:

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
```

**Windows:**
- Download Redis từ https://redis.io/download
- Hoặc sử dụng Docker: `docker run -d -p 6379:6379 redis:alpine`

**macOS:**
```bash
brew install redis
brew services start redis
```

### 4. Cấu hình môi trường
Copy file .env và chỉnh sửa theo môi trường:
```bash
cp .env .env.local
# Chỉnh sửa .env.local theo cần thiết
```

### 5. Kiểm tra hệ thống
```bash
npm run check
```

### 6. Tạo thư mục cần thiết (tự động tạo khi chạy)
```bash
mkdir -p tmp/uploads_bigfiles tmp/extracted data/final logs
```

## Chạy ứng dụng

### Development mode
```bash
# Terminal 1: Chạy server
npm run dev

# Terminal 2: Chạy worker
npm run worker
```

### Production mode
```bash
# Chạy server
npm start

# Chạy worker (terminal khác hoặc process manager)
npm run worker
```

### Sử dụng Process Manager (PM2)
```bash
# Cài đặt PM2
npm install -g pm2

# Chạy server và worker
pm2 start src/server.js --name "geoportal-server"
pm2 start src/worker.js --name "geoportal-worker"

# Xem trạng thái
pm2 status

# Xem logs
pm2 logs
```

## API Endpoints

### Upload APIs
- `POST /api/upload/init-upload` - Khởi tạo phiên upload
- `POST /api/upload/upload-chunk` - Upload chunk file
- `GET /api/upload/:uploadId/status` - Lấy trạng thái upload

### Job APIs
- `GET /api/jobs/:id/status` - Lấy trạng thái job
- `GET /api/jobs` - Lấy danh sách jobs
- `GET /api/jobs/stats` - Thống kê jobs
- `POST /api/jobs/:id/cancel` - Hủy job
- `POST /api/jobs/:id/retry` - Retry job thất bại

### System APIs
- `GET /health` - Health check

## Sử dụng API

### 1. Khởi tạo upload
```bash
curl -X POST http://localhost:3000/api/upload/init-upload \
  -H "Content-Type: application/json" \
  -d '{
    "filename": "data.zip",
    "totalChunks": 10,
    "totalSize": 104857600,
    "userId": "user123"
  }'
```

### 2. Upload chunk
```bash
curl -X POST http://localhost:3000/api/upload/upload-chunk \
  -F "uploadId=<upload-id>" \
  -F "chunkIndex=0" \
  -F "chunk=@chunk_0.bin"
```

### 3. Theo dõi job
```bash
curl http://localhost:3000/api/jobs/<job-id>/status
```

## Cấu trúc thư mục

```
src/
├── controllers/     # API controllers
├── services/        # Business logic
├── middleware/      # Express middleware
├── config/          # Configuration files
├── utils/           # Utility functions
└── worker.js        # Background job worker

tmp/                 # Temporary files
├── uploads_bigfiles/# Chunk uploads
└── extracted/       # Extracted files

data/final/          # Final processed files
```

## Testing

```bash
# Chạy tests
npm test

# Test với coverage
npm test -- --coverage

# Chạy test cụ thể
npm test -- tests/upload.test.js
```

## Maintenance

### Dọn dẹp hệ thống
```bash
# Xem trước những gì sẽ bị xóa (dry run)
npm run cleanup:dry

# Dọn dẹp file tạm (> 7 ngày)
npm run cleanup

# Dọn dẹp toàn bộ (bao gồm logs và final files)
npm run cleanup:all
```

### Monitoring
- Logs được lưu trong thư mục `logs/`
- Sử dụng `pm2 logs` để xem logs realtime
- Redis dashboard để monitor job queue

## Troubleshooting

### Lỗi thường gặp

**1. Redis connection failed**
```bash
# Kiểm tra Redis có chạy không
redis-cli ping

# Khởi động Redis
sudo systemctl start redis-server
```

**2. Permission denied khi tạo thư mục**
```bash
# Cấp quyền cho thư mục
sudo chown -R $USER:$USER ./tmp ./data ./logs
chmod -R 755 ./tmp ./data ./logs
```

**3. Worker không xử lý jobs**
- Kiểm tra Redis connection
- Kiểm tra worker process có chạy không
- Xem logs để debug

**4. File giải nén lỗi**
- Kiểm tra unrar/7z đã cài đặt chưa
- Kiểm tra file có bị corrupt không

## Cấu hình nâng cao

### Tăng hiệu suất
```env
# Tăng số worker concurrent
MAX_CONCURRENT_JOBS=5

# Tăng chunk size cho file lớn
CHUNK_SIZE=20971520  # 20MB

# Tăng timeout
JOB_TIMEOUT=3600000  # 1 hour
```

### Bảo mật
- Sử dụng HTTPS trong production
- Cấu hình CORS phù hợp
- Thêm authentication/authorization
- Giới hạn rate limiting

## Công nghệ sử dụng

- **Backend**: Node.js, Express
- **Job Queue**: BullMQ, Redis
- **File Processing**: multer, fs-extra, unzipper, node-7z
- **Security**: helmet, cors
- **Testing**: Jest, Supertest

## Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## License

MIT License - xem file [LICENSE](LICENSE) để biết thêm chi tiết.
