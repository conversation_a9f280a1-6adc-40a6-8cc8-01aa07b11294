const fs = require('fs-extra');
const path = require('path');
const config = require('../config');

class Logger {
  constructor() {
    this.logLevel = config.logging.level;
    this.logFile = config.logging.file;
    this.ensureLogDirectory();
  }

  async ensureLogDirectory() {
    const logDir = path.dirname(this.logFile);
    await fs.ensureDir(logDir);
  }

  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const metaStr = Object.keys(meta).length > 0 ? ` | ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
  }

  async writeToFile(formattedMessage) {
    try {
      await fs.appendFile(this.logFile, formattedMessage + '\n');
    } catch (error) {
      console.error('Error writing to log file:', error);
    }
  }

  shouldLog(level) {
    const levels = ['error', 'warn', 'info', 'debug'];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex <= currentLevelIndex;
  }

  async log(level, message, meta = {}) {
    if (!this.shouldLog(level)) return;

    const formattedMessage = this.formatMessage(level, message, meta);
    
    // Log to console
    console.log(formattedMessage);
    
    // Log to file
    await this.writeToFile(formattedMessage);
  }

  async error(message, meta = {}) {
    await this.log('error', message, meta);
  }

  async warn(message, meta = {}) {
    await this.log('warn', message, meta);
  }

  async info(message, meta = {}) {
    await this.log('info', message, meta);
  }

  async debug(message, meta = {}) {
    await this.log('debug', message, meta);
  }

  // Specific logging methods for different components
  async logUpload(uploadId, message, meta = {}) {
    await this.info(`[UPLOAD:${uploadId}] ${message}`, meta);
  }

  async logJob(jobId, message, meta = {}) {
    await this.info(`[JOB:${jobId}] ${message}`, meta);
  }

  async logError(component, error, meta = {}) {
    const errorMeta = {
      ...meta,
      error: error.message,
      stack: error.stack
    };
    await this.error(`[${component}] ${error.message}`, errorMeta);
  }
}

// Singleton instance
const logger = new Logger();

module.exports = logger;
