const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const uploadService = require('../services/uploadService');
const jobService = require('../services/jobService');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const config = require('../config');

// Cấu hình multer cho chunk upload
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const tempDir = path.join(config.paths.tempUploadDir, 'temp_chunks');
    await fs.ensureDir(tempDir);
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    // Tạo tên file tạm duy nhất
    const uniqueName = `chunk_${Date.now()}_${Math.round(Math.random() * 1E9)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: config.upload.chunkSize * 2 // Cho phép chunk lớn hơn một chút
  },
  fileFilter: (req, file, cb) => {
    // Chấp nhận tất cả file cho chunk upload
    cb(null, true);
  }
});

/**
 * POST /api/upload/init-upload
 * Khởi tạo phiên upload mới
 */
const initUpload = asyncHandler(async (req, res) => {
  const { filename, totalChunks, totalSize, userId } = req.body;

  // Validate input
  if (!filename || !totalChunks || !totalSize) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields: filename, totalChunks, totalSize'
    });
  }

  if (parseInt(totalChunks) <= 0 || parseInt(totalSize) <= 0) {
    return res.status(400).json({
      success: false,
      error: 'totalChunks and totalSize must be positive numbers'
    });
  }

  try {
    const result = await uploadService.initUpload(
      filename,
      parseInt(totalChunks),
      parseInt(totalSize),
      userId || 'anonymous'
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/upload/upload-chunk
 * Upload một chunk
 */
const uploadChunk = asyncHandler(async (req, res) => {
  // Sử dụng multer middleware
  upload.single('chunk')(req, res, async (err) => {
    if (err) {
      logger.logError('UploadController.uploadChunk', err);
      return res.status(400).json({
        success: false,
        error: err.message
      });
    }

    const { uploadId, chunkIndex } = req.body;
    const chunkFile = req.file;

    // Validate input
    if (!uploadId || chunkIndex === undefined || !chunkFile) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: uploadId, chunkIndex, chunk file'
      });
    }

    try {
      const result = await uploadService.uploadChunk(
        uploadId,
        parseInt(chunkIndex),
        chunkFile
      );

      // Nếu upload hoàn tất, tự động ghép file và tạo job
      if (result.isComplete) {
        try {
          // Ghép file
          const assembleResult = await uploadService.assembleFile(uploadId);
          
          // Tạo job xử lý background
          const jobResult = await jobService.createProcessingJob({
            uploadId,
            filePath: assembleResult.filePath,
            filename: assembleResult.filename,
            userId: 'anonymous' // TODO: Lấy từ auth
          });

          result.jobId = jobResult.jobId;
          result.message = 'Upload completed and processing job created';
        } catch (error) {
          logger.logError('UploadController.uploadChunk.postProcess', error);
          // Không throw error ở đây vì chunk đã upload thành công
          result.warning = 'Upload completed but failed to create processing job';
        }
      }

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });
});

/**
 * GET /api/upload/:uploadId/status
 * Lấy trạng thái upload
 */
const getUploadStatus = asyncHandler(async (req, res) => {
  const { uploadId } = req.params;

  try {
    const uploadInfo = await uploadService.getUploadInfo(uploadId);
    
    res.json({
      success: true,
      data: {
        uploadId: uploadInfo.uploadId,
        filename: uploadInfo.filename,
        status: uploadInfo.status,
        chunksReceived: uploadInfo.chunksReceived,
        totalChunks: uploadInfo.totalChunks,
        progress: Math.round((uploadInfo.chunksReceived / uploadInfo.totalChunks) * 100),
        createdAt: uploadInfo.createdAt,
        lastChunkAt: uploadInfo.lastChunkAt,
        completedAt: uploadInfo.completedAt
      }
    });
  } catch (error) {
    res.status(404).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = {
  initUpload,
  uploadChunk,
  getUploadStatus
};
