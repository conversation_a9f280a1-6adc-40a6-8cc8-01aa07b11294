const request = require('supertest');
const fs = require('fs-extra');
const path = require('path');
const app = require('../src/server');
const uploadService = require('../src/services/uploadService');

describe('Upload API', () => {
  const testFile = {
    name: 'test.zip',
    size: 1024 * 1024, // 1MB
    content: Buffer.alloc(1024 * 1024, 'test data')
  };

  beforeAll(async () => {
    // Ensure test directories exist
    await fs.ensureDir('./tmp/uploads_bigfiles');
    await fs.ensureDir('./tmp/extracted');
    await fs.ensureDir('./data/final');
  });

  afterEach(async () => {
    // Clean up test files
    try {
      await fs.remove('./tmp/uploads_bigfiles');
      await fs.ensureDir('./tmp/uploads_bigfiles');
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('POST /api/upload/init-upload', () => {
    it('should initialize upload successfully', async () => {
      const response = await request(app)
        .post('/api/upload/init-upload')
        .send({
          filename: testFile.name,
          totalChunks: 1,
          totalSize: testFile.size,
          userId: 'test-user'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('uploadId');
      expect(response.body.data).toHaveProperty('chunkSize');
    });

    it('should reject invalid file extension', async () => {
      const response = await request(app)
        .post('/api/upload/init-upload')
        .send({
          filename: 'test.txt',
          totalChunks: 1,
          totalSize: testFile.size,
          userId: 'test-user'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('extension not allowed');
    });

    it('should reject file too large', async () => {
      const response = await request(app)
        .post('/api/upload/init-upload')
        .send({
          filename: testFile.name,
          totalChunks: 1,
          totalSize: 200 * 1024 * 1024 * 1024, // 200GB
          userId: 'test-user'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('too large');
    });

    it('should reject missing required fields', async () => {
      const response = await request(app)
        .post('/api/upload/init-upload')
        .send({
          filename: testFile.name
          // Missing totalChunks and totalSize
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required fields');
    });
  });

  describe('POST /api/upload/upload-chunk', () => {
    let uploadId;

    beforeEach(async () => {
      // Initialize upload first
      const initResponse = await request(app)
        .post('/api/upload/init-upload')
        .send({
          filename: testFile.name,
          totalChunks: 1,
          totalSize: testFile.size,
          userId: 'test-user'
        });

      uploadId = initResponse.body.data.uploadId;
    });

    it('should upload chunk successfully', async () => {
      const response = await request(app)
        .post('/api/upload/upload-chunk')
        .field('uploadId', uploadId)
        .field('chunkIndex', '0')
        .attach('chunk', testFile.content, testFile.name);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.chunksReceived).toBe(1);
      expect(response.body.data.isComplete).toBe(true);
    });

    it('should reject invalid uploadId', async () => {
      const response = await request(app)
        .post('/api/upload/upload-chunk')
        .field('uploadId', 'invalid-id')
        .field('chunkIndex', '0')
        .attach('chunk', testFile.content, testFile.name);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });

    it('should reject missing chunk file', async () => {
      const response = await request(app)
        .post('/api/upload/upload-chunk')
        .field('uploadId', uploadId)
        .field('chunkIndex', '0');

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required fields');
    });
  });

  describe('GET /api/upload/:uploadId/status', () => {
    let uploadId;

    beforeEach(async () => {
      const initResponse = await request(app)
        .post('/api/upload/init-upload')
        .send({
          filename: testFile.name,
          totalChunks: 1,
          totalSize: testFile.size,
          userId: 'test-user'
        });

      uploadId = initResponse.body.data.uploadId;
    });

    it('should get upload status successfully', async () => {
      const response = await request(app)
        .get(`/api/upload/${uploadId}/status`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('uploadId');
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data).toHaveProperty('progress');
    });

    it('should return 404 for invalid uploadId', async () => {
      const response = await request(app)
        .get('/api/upload/invalid-id/status');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });
});

describe('Health Check', () => {
  it('should return health status', async () => {
    const response = await request(app).get('/health');

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.message).toContain('running');
  });
});
