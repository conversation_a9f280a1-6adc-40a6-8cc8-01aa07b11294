const fs = require('fs-extra');
const path = require('path');
const Redis = require('redis');
const config = require('../src/config');

/**
 * <PERSON><PERSON>t kiểm tra hệ thống trước khi ch<PERSON>
 */
class SystemChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  async checkAll() {
    console.log('🔍 Checking system requirements...\n');

    await this.checkDirectories();
    await this.checkRedis();
    await this.checkDependencies();
    await this.checkPermissions();

    this.printResults();
    
    if (this.errors.length > 0) {
      process.exit(1);
    }
  }

  async checkDirectories() {
    console.log('📁 Checking directories...');
    
    const requiredDirs = [
      config.paths.tempUploadDir,
      config.paths.tempExtractDir,
      config.paths.finalDataDir,
      config.paths.logsDir
    ];

    for (const dir of requiredDirs) {
      try {
        await fs.ensureDir(dir);
        console.log(`  ✅ ${dir}`);
      } catch (error) {
        this.errors.push(`Cannot create directory: ${dir} - ${error.message}`);
        console.log(`  ❌ ${dir}`);
      }
    }
  }

  async checkRedis() {
    console.log('\n🔴 Checking Redis connection...');
    
    try {
      const client = Redis.createClient({
        host: config.redis.host,
        port: config.redis.port,
        password: config.redis.password
      });

      await client.connect();
      await client.ping();
      await client.disconnect();
      
      console.log(`  ✅ Redis connected at ${config.redis.host}:${config.redis.port}`);
    } catch (error) {
      this.errors.push(`Redis connection failed: ${error.message}`);
      console.log(`  ❌ Redis connection failed`);
    }
  }

  async checkDependencies() {
    console.log('\n📦 Checking dependencies...');
    
    const requiredCommands = [
      { cmd: 'unrar', optional: true, desc: 'RAR extraction' },
      { cmd: '7z', optional: true, desc: '7Z extraction' }
    ];

    for (const { cmd, optional, desc } of requiredCommands) {
      try {
        const { spawn } = require('child_process');
        const process = spawn(cmd, ['--version'], { stdio: 'ignore' });
        
        await new Promise((resolve, reject) => {
          process.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Command failed with code ${code}`));
            }
          });
          process.on('error', reject);
        });
        
        console.log(`  ✅ ${cmd} (${desc})`);
      } catch (error) {
        if (optional) {
          this.warnings.push(`Optional dependency missing: ${cmd} - ${desc}`);
          console.log(`  ⚠️  ${cmd} (${desc}) - Optional`);
        } else {
          this.errors.push(`Required dependency missing: ${cmd} - ${desc}`);
          console.log(`  ❌ ${cmd} (${desc}) - Required`);
        }
      }
    }
  }

  async checkPermissions() {
    console.log('\n🔐 Checking permissions...');
    
    const testDirs = [
      config.paths.tempUploadDir,
      config.paths.tempExtractDir,
      config.paths.finalDataDir,
      config.paths.logsDir
    ];

    for (const dir of testDirs) {
      try {
        const testFile = path.join(dir, 'test-write.tmp');
        await fs.writeFile(testFile, 'test');
        await fs.remove(testFile);
        console.log(`  ✅ Write permission: ${dir}`);
      } catch (error) {
        this.errors.push(`No write permission: ${dir} - ${error.message}`);
        console.log(`  ❌ Write permission: ${dir}`);
      }
    }
  }

  printResults() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 SYSTEM CHECK RESULTS');
    console.log('='.repeat(50));

    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('🎉 All checks passed! System is ready to run.');
    } else {
      if (this.errors.length > 0) {
        console.log('\n❌ ERRORS:');
        this.errors.forEach(error => console.log(`  - ${error}`));
      }

      if (this.warnings.length > 0) {
        console.log('\n⚠️  WARNINGS:');
        this.warnings.forEach(warning => console.log(`  - ${warning}`));
      }

      if (this.errors.length > 0) {
        console.log('\n🚨 Please fix the errors above before running the system.');
      } else {
        console.log('\n✅ System can run, but some optional features may not work.');
      }
    }
    
    console.log('='.repeat(50));
  }
}

// Run check if this file is executed directly
if (require.main === module) {
  const checker = new SystemChecker();
  checker.checkAll().catch(console.error);
}

module.exports = SystemChecker;
