// Test setup file
process.env.NODE_ENV = 'test';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

// Mock Redis if not available
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    ping: jest.fn(),
    disconnect: jest.fn(),
    on: jest.fn()
  }))
}));

// Mock BullMQ if Redis not available
jest.mock('bullmq', () => ({
  Queue: jest.fn(() => ({
    add: jest.fn(),
    getJobs: jest.fn(() => []),
    on: jest.fn()
  })),
  Worker: jest.fn(() => ({
    on: jest.fn(),
    close: jest.fn()
  }))
}));
