{"version": 3, "file": "sandbox.js", "sourceRoot": "", "sources": ["../../../src/classes/sandbox.ts"], "names": [], "mappings": ";;AAAA,oCAAuD;AAKvD,MAAM,OAAO,GAAG,CACd,WAAgB,EAChB,SAAoB,EACpB,EAAE;IACF,OAAO,KAAK,UAAU,OAAO,CAAC,GAAiB,EAAE,KAAc;QAC7D,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,UAAe,CAAC;QACpB,IAAI,WAAgB,CAAC;QAErB,MAAM,KAAK,CAAC,IAAI,CAAC;YACf,GAAG,EAAE,oBAAY,CAAC,KAAK;YACvB,GAAG,EAAE,GAAG,CAAC,aAAa,EAAE;YACxB,KAAK;SACN,CAAC,CAAC;QAEH,MAAM,IAAI,GAAe,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACvD,UAAU,GAAG,KAAK,EAAE,GAAiB,EAAE,EAAE;;gBACvC,QAAQ,GAAG,CAAC,GAAG,EAAE;oBACf,KAAK,qBAAa,CAAC,SAAS;wBAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACnB,MAAM;oBACR,KAAK,qBAAa,CAAC,MAAM,CAAC;oBAC1B,KAAK,qBAAa,CAAC,KAAK,CAAC,CAAC;wBACxB,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;wBACxB,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;wBAC9B,MAAM,CAAC,GAAG,CAAC,CAAC;wBACZ,MAAM;qBACP;oBACD,KAAK,qBAAa,CAAC,QAAQ;wBACzB,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACpC,MAAM;oBACR,KAAK,qBAAa,CAAC,GAAG;wBACpB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACzB,MAAM;oBACR,KAAK,qBAAa,CAAC,aAAa;wBAC9B,MAAM,GAAG,CAAC,aAAa,CAAC,MAAA,GAAG,CAAC,KAAK,0CAAE,SAAS,EAAE,MAAA,GAAG,CAAC,KAAK,0CAAE,KAAK,CAAC,CAAC;wBAChE,MAAM;oBACR,KAAK,qBAAa,CAAC,MAAM;wBACvB,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBAChC,MAAM;iBACT;YACH,CAAC,CAAC;YAEF,WAAW,GAAG,CAAC,QAAa,EAAE,MAAW,EAAE,EAAE;gBAC3C,MAAM,CACJ,IAAI,KAAK,CAAC,wBAAwB,GAAG,QAAQ,GAAG,WAAW,GAAG,MAAM,CAAC,CACtE,CAAC;YACJ,CAAC,CAAC;YAEF,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAChC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,IAAI;YACF,MAAM,IAAI,CAAC;YACX,OAAO,IAAI,CAAC;SACb;gBAAS;YACR,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YACjC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAE/B,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE;gBAClE,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACzB;iBAAM;gBACL,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC1B;SACF;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,OAAO,CAAC"}